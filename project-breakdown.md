# Project Breakdown: Manga/Comic Applications

This document provides a comprehensive overview of the manga and comic-related applications in this repository.

## 1. HakuNeko
*Cross-platform manga/anime downloader*

### Core Features
- **Multi-source downloader**: Downloads manga and anime from various websites through connector plugins
- **Cross-platform support**: Available for Windows, macOS, and Linux
- **Web-based interface**: Built with Polymer framework for the frontend
- **Connector system**: Modular architecture with plugins for different manga/anime sites
- **Bookmark management**: Save and organize favorite series
- **Download management**: Queue system with progress tracking
- **Chapter marking**: Track reading progress
- **Discord integration**: Rich presence support

### Architecture
- **Frontend**: Polymer-based web UI with classic light/dark themes
- **Backend**: Electron desktop app with Node.js
- **Engine**: Modular connector system for website scrapers
- **Storage**: Local file system with organized directory structure
- **IPC**: Inter-process communication between web and desktop layers

### Key Functions
- Browse and search manga/anime from multiple sources
- Download chapters/episodes with metadata
- Organize downloads in structured folders
- Preview pages before downloading
- Blacklist management for unwanted content

---

## 2. JHenTai
*E-Hentai client application*

### Core Features
- **Multi-platform Flutter app**: Android, iOS, Windows, macOS, Linux support
- **E-Hentai integration**: Dedicated client for E-Hentai/ExHentai
- **Advanced reading modes**: Horizontal, vertical, double-column layouts
- **Download system**: Gallery and archive downloading with resume capability
- **Search functionality**: Advanced search with tag suggestions
- **User management**: Login, favorites, watched lists, history
- **Responsive layouts**: Mobile, tablet, and desktop optimized interfaces

### Architecture
- **Framework**: Flutter with GetX state management
- **Services**: Modular service architecture (download, gallery, archive, etc.)
- **Networking**: Custom HTTP clients for E-Hentai API
- **Storage**: Local database for caching and offline reading
- **Multi-language**: Support for English, Chinese, Korean, Portuguese, Russian

### Key Functions
- Browse E-Hentai galleries with multiple view styles
- Advanced search with tag filtering and suggestions
- Download galleries and archives with progress tracking
- Multiple reading layouts and zoom controls
- Favorites and reading history management
- Tag translation and search optimization
- Super resolution image enhancement
- Local gallery import and management

---

## 3. Komga
*Media server for comics and manga*

### Core Features
- **Media server**: Self-hosted server for comics, manga, BDs, magazines, eBooks
- **Web reader**: Built-in reader with multiple reading modes
- **User management**: Multi-user support with access controls and restrictions
- **Library organization**: Collections, read lists, and metadata management
- **API integration**: REST API for third-party tools and scripts
- **OPDS support**: v1 and v2 for e-reader compatibility
- **Sync capabilities**: Kobo and KOReader synchronization

### Architecture
- **Backend**: Spring Boot (Kotlin) with embedded database
- **Frontend**: Vue.js web application
- **Database**: H2/SQLite for metadata storage
- **API**: RESTful API with OpenAPI documentation
- **Desktop wrapper**: Optional tray application

### Key Functions
- Scan and organize comic/manga libraries
- Automatic metadata extraction and editing
- Multi-user access with per-library permissions
- Web-based reading with responsive design
- Duplicate detection and management
- Book import and series organization
- Download management for books and series
- Integration with external reading devices

---

## 4. Teemii
*Streamlined manga reading platform*

### Core Features
- **Web-based manga reader**: In-browser reading experience
- **Metadata aggregation**: Powerful metadata collection and management
- **Cross-platform access**: Web application accessible from any device
- **Automated updates**: Automatic collection updates
- **Scrobbler integration**: Sync with Kitsu and AniList
- **Dark mode**: Comfortable reading in various lighting conditions

### Architecture
- **Frontend**: Vue.js 3 with Vite build system
- **Backend**: Node.js/Express server with SQLite database
- **API**: RESTful API with v1 and v2 endpoints
- **Task system**: Background job processing for downloads and updates
- **Agent system**: Automated manga source management

### Key Functions
- Manga library management and organization
- Chapter fetching from multiple online sources
- Reading progress tracking and synchronization
- Search and discovery features
- Backup and restore functionality
- User authentication and preferences
- Integration with external tracking services
- Automated manga updates and notifications

---

## 5. Kuroibara (Bonus)
*Comprehensive manga library application*

### Core Features
- **100+ provider support**: Extensive manga source integration
- **2FA authentication**: Enhanced security features
- **Library management**: Categories, reading lists, bookmarks
- **File import**: Support for CBZ, CBR, 7Z formats
- **Background tasks**: Download and update management

### Architecture
- **Frontend**: Vue.js with Tailwind CSS
- **Backend**: FastAPI (Python) with comprehensive API
- **Provider system**: Flexible, configurable manga source providers
- **Task management**: Background job processing system

### Key Functions
- User authentication with 2FA support
- Manga search across 100+ providers
- Library management with categories and reading lists
- Reading progress tracking and bookmarking
- Background task management for downloads
- File import functionality (CBZ, CBR, 7Z)
- Directory import for chapters

---

## Use Case Summary

Each project serves different use cases:

- **HakuNeko**: General-purpose manga/anime downloader with broad source support
- **JHenTai**: Specialized E-Hentai client with advanced reading features
- **Komga**: Self-hosted media server for personal comic/manga collections
- **Teemii**: Streamlined web-based manga reader with modern UI
- **Kuroibara**: Comprehensive library manager with extensive provider support

## Technology Stack Overview

| Project | Frontend | Backend | Database | Platform |
|---------|----------|---------|----------|----------|
| HakuNeko | Polymer | Node.js/Electron | File System | Desktop |
| JHenTai | Flutter | Flutter Services | Local DB | Multi-platform |
| Komga | Vue.js | Spring Boot (Kotlin) | H2/SQLite | Web/Server |
| Teemii | Vue.js 3 | Node.js/Express | SQLite | Web |
| Kuroibara | Vue.js | FastAPI (Python) | Database | Web |
