PODS:
  - audio_session (0.0.1):
    - FlutterMacOS
  - battery_plus (0.0.1):
    - FlutterMacOS
  - desktop_webview_window (0.0.1):
    - FlutterMacOS
  - device_info_plus (0.0.1):
    - FlutterMacOS
  - FlutterMacOS (1.0.0)
  - just_audio (0.0.1):
    - FlutterMacOS
  - package_info_plus (0.0.1):
    - FlutterMacOS
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - screen_brightness_macos (0.1.0):
    - FlutterMacOS
  - screen_retriever (0.0.1):
    - FlutterMacOS
  - share_plus (0.0.1):
    - FlutterMacOS
  - smart_auth (0.0.1):
    - FlutterMacOS
  - sqflite (0.0.3):
    - Flutter
    - FlutterMacOS
  - sqlite3 (3.39.4):
    - sqlite3/common (= 3.39.4)
  - sqlite3/common (3.39.4)
  - sqlite3/fts5 (3.39.4):
    - sqlite3/common
  - sqlite3/json1 (3.39.4):
    - sqlite3/common
  - sqlite3/perf-threadsafe (3.39.4):
    - sqlite3/common
  - sqlite3/rtree (3.39.4):
    - sqlite3/common
  - sqlite3_flutter_libs (0.0.1):
    - FlutterMacOS
    - sqlite3 (~> 3.39.0)
    - sqlite3/fts5
    - sqlite3/json1
    - sqlite3/perf-threadsafe
    - sqlite3/rtree
  - url_launcher_macos (0.0.1):
    - FlutterMacOS
  - video_player_avfoundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - wakelock_plus (0.0.1):
    - FlutterMacOS
  - window_manager (0.2.0):
    - FlutterMacOS

DEPENDENCIES:
  - audio_session (from `Flutter/ephemeral/.symlinks/plugins/audio_session/macos`)
  - battery_plus (from `Flutter/ephemeral/.symlinks/plugins/battery_plus/macos`)
  - desktop_webview_window (from `Flutter/ephemeral/.symlinks/plugins/desktop_webview_window/macos`)
  - device_info_plus (from `Flutter/ephemeral/.symlinks/plugins/device_info_plus/macos`)
  - FlutterMacOS (from `Flutter/ephemeral`)
  - just_audio (from `Flutter/ephemeral/.symlinks/plugins/just_audio/macos`)
  - package_info_plus (from `Flutter/ephemeral/.symlinks/plugins/package_info_plus/macos`)
  - path_provider_foundation (from `Flutter/ephemeral/.symlinks/plugins/path_provider_foundation/darwin`)
  - screen_brightness_macos (from `Flutter/ephemeral/.symlinks/plugins/screen_brightness_macos/macos`)
  - screen_retriever (from `Flutter/ephemeral/.symlinks/plugins/screen_retriever/macos`)
  - share_plus (from `Flutter/ephemeral/.symlinks/plugins/share_plus/macos`)
  - smart_auth (from `Flutter/ephemeral/.symlinks/plugins/smart_auth/macos`)
  - sqflite (from `Flutter/ephemeral/.symlinks/plugins/sqflite/darwin`)
  - sqlite3_flutter_libs (from `Flutter/ephemeral/.symlinks/plugins/sqlite3_flutter_libs/macos`)
  - url_launcher_macos (from `Flutter/ephemeral/.symlinks/plugins/url_launcher_macos/macos`)
  - video_player_avfoundation (from `Flutter/ephemeral/.symlinks/plugins/video_player_avfoundation/darwin`)
  - wakelock_plus (from `Flutter/ephemeral/.symlinks/plugins/wakelock_plus/macos`)
  - window_manager (from `Flutter/ephemeral/.symlinks/plugins/window_manager/macos`)

SPEC REPOS:
  trunk:
    - sqlite3

EXTERNAL SOURCES:
  audio_session:
    :path: Flutter/ephemeral/.symlinks/plugins/audio_session/macos
  battery_plus:
    :path: Flutter/ephemeral/.symlinks/plugins/battery_plus/macos
  desktop_webview_window:
    :path: Flutter/ephemeral/.symlinks/plugins/desktop_webview_window/macos
  device_info_plus:
    :path: Flutter/ephemeral/.symlinks/plugins/device_info_plus/macos
  FlutterMacOS:
    :path: Flutter/ephemeral
  just_audio:
    :path: Flutter/ephemeral/.symlinks/plugins/just_audio/macos
  package_info_plus:
    :path: Flutter/ephemeral/.symlinks/plugins/package_info_plus/macos
  path_provider_foundation:
    :path: Flutter/ephemeral/.symlinks/plugins/path_provider_foundation/darwin
  screen_brightness_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/screen_brightness_macos/macos
  screen_retriever:
    :path: Flutter/ephemeral/.symlinks/plugins/screen_retriever/macos
  share_plus:
    :path: Flutter/ephemeral/.symlinks/plugins/share_plus/macos
  smart_auth:
    :path: Flutter/ephemeral/.symlinks/plugins/smart_auth/macos
  sqflite:
    :path: Flutter/ephemeral/.symlinks/plugins/sqflite/darwin
  sqlite3_flutter_libs:
    :path: Flutter/ephemeral/.symlinks/plugins/sqlite3_flutter_libs/macos
  url_launcher_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/url_launcher_macos/macos
  video_player_avfoundation:
    :path: Flutter/ephemeral/.symlinks/plugins/video_player_avfoundation/darwin
  wakelock_plus:
    :path: Flutter/ephemeral/.symlinks/plugins/wakelock_plus/macos
  window_manager:
    :path: Flutter/ephemeral/.symlinks/plugins/window_manager/macos

SPEC CHECKSUMS:
  audio_session: dea1f41890dbf1718f04a56f1d6150fd50039b72
  battery_plus: 922e3d9686072259c8fbce6c4238561f769990ae
  desktop_webview_window: d4365e71bcd4e1aa0c14cf0377aa24db0c16a7e2
  device_info_plus: ce1b7762849d3ec103d0e0517299f2db7ad60720
  FlutterMacOS: 8f6f14fa908a6fb3fba0cd85dbd81ec4b251fb24
  just_audio: 9b67ca7b97c61cfc9784ea23cd8cc55eb226d489
  package_info_plus: fa739dd842b393193c5ca93c26798dff6e3d0e0c
  path_provider_foundation: 2b6b4c569c0fb62ec74538f866245ac84301af46
  screen_brightness_macos: 2d6d3af2165592d9a55ffcd95b7550970e41ebda
  screen_retriever: 59634572a57080243dd1bf715e55b6c54f241a38
  share_plus: 36537c04ce0c3e3f5bd297ce4318b6d5ee5fd6cf
  smart_auth: b38e3ab4bfe089eacb1e233aca1a2340f96c28e9
  sqflite: 673a0e54cc04b7d6dba8d24fb8095b31c3a99eec
  sqlite3: 401936402fe5aa242c07f87fccfee5fc2b606f45
  sqlite3_flutter_libs: ce22cdcbb9c8945707a40c52d7966976b4550528
  url_launcher_macos: 5f437abeda8c85500ceb03f5c1938a8c5a705399
  video_player_avfoundation: 7c6c11d8470e1675df7397027218274b6d2360b3
  wakelock_plus: 4783562c9a43d209c458cb9b30692134af456269
  window_manager: 3a1844359a6295ab1e47659b1a777e36773cd6e8

PODFILE CHECKSUM: 0d3963a09fc94f580682bd88480486da345dc3f0

COCOAPODS: 1.15.2
