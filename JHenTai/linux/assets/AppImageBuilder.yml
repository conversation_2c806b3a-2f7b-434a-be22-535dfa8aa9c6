version: 1
AppDir:
  path: ../../build/linux/AppDir
  app_info:
    id: top.jtmonster.jhentai
    name: JHenTai
    icon: top.jtmonster.jhentai
    version: latest
    exec: jhentai
    exec_args: $@
  files:
    include:
    - /lib64/ld-linux-x86-64.so.2
    exclude:
    - usr/share/man
    - usr/share/doc/*/README.*
    - usr/share/doc/*/changelog.*
    - usr/share/doc/*/NEWS.*
    - usr/share/doc/*/TODO.*
  test:
    fedora-30:
      image: appimagecrafters/tests-env:fedora-30
      command: ./AppRun
    debian-stable:
      image: appimagecrafters/tests-env:debian-stable
      command: ./AppRun
    archlinux-latest:
      image: appimagecrafters/tests-env:archlinux-latest
      command: ./AppRun
    centos-7:
      image: appimagecrafters/tests-env:centos-7
      command: ./AppRun
    ubuntu-xenial:
      image: appimagecrafters/tests-env:ubuntu-xenial
      command: ./AppRun
AppImage:
  arch: x86_64
  update-information: guess