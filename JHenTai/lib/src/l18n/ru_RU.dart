import 'dart:core';

class ru_RU {
  static Map<String, String> keys() {
    return {
      /// common
      'yes': 'Да',
      'no': 'Нет',
      'cancel': "Отме<PERSON>",
      'OK': "ОК",
      'reset': "Сбросить",
      'success': "Успешно",
      'error': "Ош<PERSON>бка",
      'failed': "Не удалось",
      'reload': 'Перезагрузить',
      'noMoreData': 'Больше нет',
      'noData': 'Нет данных',
      'operationFailed': 'Операция не удалась',
      'needLoginToOperate': 'Требуется вход для выполнения операции',
      'hasCopiedToClipboard': "Скопировано в буфер обмена",
      'networkError': "Сетевая ошибка, Нажмите для перезагрузки",
      'systemError': "Системная ошибка",
      'invalid': "Недопустимо",
      'internalError': "Внутренняя ошибка",
      'you': 'Вы',
      'retryHint': 'Пожалуйста, повторите попытку после обновления',
      'stop': 'Остановить',
      'attention': 'Внимание',
      'jump': 'Перейти',
      'deleteAll': 'Удалить все',
      'connectionTimeoutHint': 'Тайм-аут подключения к сети',
      'receiveDataTimeoutHint': 'Тайм-аут получения данных',
      'archiveError': 'Ошибка загрузки архива',
      'edit': 'Редактировать',

      'home': "Главная",
      'gallery': "Галерея",
      'setting': 'Настройки',

      /// schedule
      'dawnOfaNewDay': 'Наступил рассвет нового дня!',
      'encounterMonster': 'Вы встретили монстра!',
      'encounterMonsterHint': 'Нажмите, чтобы сразиться в HentaiVerse.',

      /// unlock page
      'localizedReason': 'Пожалуйста, пройдите аутентификацию для продолжения',
      'tap2Auth': 'Нажмите для аутентификации',
      'passwordErrorHint': 'Ошибка пароля, попробуйте еще раз',

      /// start page
      'TapAgainToExit': 'Нажмите еще раз для выхода',

      /// update dialog
      'availableUpdate': 'Доступно обновление!',
      'LatestVersion': 'Последняя версия',
      'CurrentVersion': 'Текущая версия',
      'check': 'Проверить',
      'dismiss': 'Закрыть',

      /// login page
      'login': 'Вход',
      'notLoggedIn': 'Войти',
      'logout': 'Выйти',
      'passwordLogin': 'Вход по паролю',
      'cookieLogin': 'Вход по Cookie',
      'youHaveLoggedInAs': 'Здравствуйте:   ',
      'cookieIsBlack': 'Cookie недействителен',
      'cookieFormatError': 'Ошибка формата Cookie',
      'invalidCookie': 'Ошибка входа или недействительный cookie',
      'loginFail': 'Ошибка входа',
      'userName': 'Имя пользователя',
      'EHUser': 'Пользователь EH',
      'password': 'Пароль',
      'needCaptcha': 'Требуется капча, пожалуйста, войдите снова через cookie или веб.',
      'userNameOrPasswordMismatch': 'Неверное имя пользователя или пароль',
      'copyCookies': 'Скопировать Cookies',
      'tap2Copy': 'Нажмите, чтобы скопировать',
      'webLoginIsDisabled': 'Веб-вход отключен на десктопе',
      'loginSuccess': 'Вход выполнен успешно',
      'userNameFormHint': 'Попробуйте войти с cookie в случае Sad Panda',
      'tap2Login': 'Войти',
      'parse': 'Парсить',
      'igneousHint': 'igneous (требуется для EX)',
      'refreshIgneousFailed': 'Не удалось обновить Igneous',

      /// request
      'sadPanda': 'Sad Panda (нет данных). См.: https://github.com/jiangtian616/JHenTai/wiki/Common-Questions',
      // Оставляем Sad Panda
      'sadPandaReferLink': 'https://github.com/jiangtian616/JHenTai/wiki/Common-Questions',
      // URL не переводим

      /// gallery card
      'filtered': 'Отфильтровано',

      /// gallery page
      'getGallerysFailed': "Не удалось получить галереи",
      'refreshGalleryFailed': 'Не удалось обновить галерею',
      'tabBarSetting': 'Настройка панели вкладок',
      'jumpPageTo': 'Перейти к странице',
      'range': 'Диапазон',
      'current': 'Текущая',
      'galleryUrlDetected': 'URL галереи найден в буфере обмена',
      'galleryUrlDetectedHint': 'Нажмите, чтобы перейти на страницу деталей',

      /// details page
      'read': 'Читать',
      'download': 'Загрузить',
      'favorite': 'В избранное',
      'rating': 'Рейтинг',
      'torrent': 'Торрент',
      'archive': 'Архив',
      'statistic': 'Статистика',
      'similar': 'Похожие',
      'downloading': "Загрузка",
      'resume': "Возобновить",
      'pause': 'Пауза',
      'finished': 'Завершено',
      'update': 'Обновить',
      'submit': 'Отправить',
      'chooseFavorite': 'Выбрать избранное',
      'asYourDefault': 'По умолчанию',
      'Note': 'Заметка',
      'addNoteHint': 'Выберите слот перед добавлением заметки',
      'uploader': 'Загрузил(а)',
      'allComments': 'Все комментарии',
      'noComments': 'Нет комментариев',
      'lastEditedOn': 'Последнее редактирование:',
      'getGalleryDetailFailed': 'Не удалось получить детали галереи',
      'invisible2User': 'Эта галерея невидима для Вас',
      'invisibleHints': 'Эта галерея удалена или недоступна.',
      'copyRightHints': 'Эта галерея недоступна из-за претензии по авторским правам от: ',
      'refreshGalleryDetailsFailed': 'Не удалось обновить детали галереи',
      'failToGetThumbnails': "Не удалось получить миниатюры",
      'favoriteGallerySuccess': "Галерея успешно добавлена в избранное",
      'favoriteGalleryFailed': "Не удалось добавить галерею в избранное",
      'removeFavoriteSuccess': "Успешно удалено из избранного",
      'removeFavoriteFailed': "Не удалось удалить из избранного",
      'getGalleryFavoriteInfoFailed': 'Не удалось получить информацию об избранном для галереи',
      'favoriteNoteSlotFullHint': 'Слоты заметок избранного заполнены, пожалуйста, сначала удалите некоторые заметки',
      'ratingSuccess': 'Рейтинг успешно выставлен',
      'ratingFailed': 'Не удалось выставить рейтинг',
      'voteTagFailed': 'Не удалось проголосовать за тег',
      'beginToDownload': 'Начать загрузку',
      'resumeDownload': 'Возобновить загрузку',
      'pauseDownload': 'Приостановить загрузку',
      'addNewTagSetSuccess': 'Новый набор тегов успешно добавлен',
      'addNewWatchedTagSetSuccess': 'Новый набор отслеживаемых тегов успешно добавлен',
      'addNewHiddenTagSetSuccess': 'Новый набор скрытых тегов успешно добавлен',
      'addNewTagSetSuccessHint': 'Вы можете проверить свои теги в Настройки -> EH -> Мои теги',
      'addNewTagSetFailed': 'Не удалось добавить новый набор тегов',
      'VisitorStatistics': 'Статистика посетителей',
      'invisible2UserWithoutDonation': 'Статистика этой галереи невидима для пользователей без доната',
      'getGalleryStatisticsFailed': 'Не удалось получить статистику галереи',
      'totalVisits': 'Всего посещений',
      'visits': 'Посещений',
      'imageAccesses': 'Доступов к изображениям',
      'period': 'Период',
      'ranking': 'Место',
      'score': 'Оценка',
      'NotOnTheList': 'Нет в списке',
      'getGalleryArchiveFailed': 'Не удалось получить архив галереи',
      'parseGalleryArchiveFailed':
          'Ошибка парсинга, убедитесь, что ваши [Настройки архиватора] на e-hentai установлены как [Ручной выбор, Ручной запуск (По умолчанию)]',
      'original': 'Оригинал',
      'resample': 'Уменьшенная',
      'beginToDownloadArchive': 'Начать загрузку архива',
      'beginToDownloadArchiveHint': 'Вы можете проверить прогресс в Загрузки -> Архив',
      'updateGalleryError': 'Ошибка обновления галереи',
      'thisGalleryHasANewVersion': 'У этой галереи есть новая версия',
      'hasUpdated': 'Обновлено',
      'unpackingArchiveError': 'Ошибка распаковки архива',
      'failedToDealWith': 'Не удалось обработать',
      'hasDownloaded': 'Уже загружено',
      '410Hints': 'Вы скачали слишком много байт из этого архива, и требуется повторная разблокировка для возобновления.',
      '429Hints': 'Слишком много запросов на загрузку! Вам лучше уменьшить количество одновременных загрузок архивов.',
      'getUnpackedImagesFailedMsg': 'JHenTai не может загрузить изображения этого архива, пожалуйста, проверьте локальный файл.',
      // Оставляем JHenTai
      'getGalleryTorrentsFailed': 'Не удалось получить торренты',
      'chooseArchive': 'Выбрать архив',
      'tagSetExceedLimit': 'Нельзя добавить больше тегов, так как вы достигли лимита',
      'useTranslation': 'Использовать перевод',
      'addTagSuccess': 'Тег успешно добавлен',
      'addTagFailed': 'Не удалось добавить тег',
      'parentGallery': 'Родительская галерея',
      'blockUploaderLocally': 'Заблокировать пользователя локально',

      /// detail dialog
      'galleryUrl': 'URL Галереи',
      'title': 'Название',
      'japaneseTitle': 'Японское название',
      'category': 'Категория',
      'publishTime': 'Время публикации',
      'pageCount': 'Кол-во страниц',
      'favoriteCount': 'В избранном',
      'ratingCount': 'Оценок',

      /// comment page
      'newComment': 'Новый комментарий',
      'updateComment': 'Обновить комментарий',
      'commentTooShort': 'Комментарий слишком короткий',
      'sendCommentFailed': 'Не удалось отправить комментарий',
      'voteCommentFailed': 'Не удалось проголосовать за комментарий',
      'voteCommentFailedHint': 'Попробуйте сначала потянуть вниз для обновления страницы деталей',
      'unknownUser': 'Неизвестный пользователь',
      'atLeast3Characters': 'Минимум 3 символа',
      'noJHenTaiHints': 'Пожалуйста, не упоминайте JHenTai, спасибо',
      'blockUser': 'Заблокировать пользователя',

      /// EHImage
      'reloadImage': "Перезагрузить изображение",

      /// read page
      'parsingPage': "Анализ страницы",
      'parsingURL': "Анализ URL",
      'parsePageFailed': "Ошибка парсинга страницы, Нажмите для повтора",
      'parseURLFailed': "Ошибка парсинга URL, Нажмите для повтора",
      'loading': "Загрузка",
      'paused': 'На паузе',
      'exceedImageLimits': "Превышен лимит изображений",
      'ehServerError': 'Произошла ошибка на сервере EH, попробуйте позже',
      'unsupportedImagePageStyle': "JHenTai не поддерживает Multi-Page Viewer(MPV), пожалуйста, измените стиль на стандартный на e-hentai.org",
      // Оставляем JHenTai, MPV, e-hentai.org
      'toNext': 'К следующей',
      'toPrev': 'К предыдущей',
      'back': 'Назад',
      'toggleMenu': 'Переключить меню',
      'share': 'Поделиться',
      'save': 'Сохранить в Изображения',

      /// setting page
      'account': 'Аккаунт',
      'EH': 'EH',
      'style': 'Стиль',
      'preference': 'Предпочтения',
      'network': 'Сеть',
      'performance': 'Производительность',
      'mouseWheel': 'Колесо мыши',
      'advanced': 'Расширенные',
      'cloud': 'Облако',
      'security': 'Безопасность',
      'about': 'О приложении',
      'accountSetting': 'Настройка аккаунта',
      'styleSetting': 'Настройка стиля',
      'advancedSetting': 'Расширенные настройки',
      'securitySetting': 'Настройки безопасности',
      'ehSetting': 'Настройки сайта EH',
      'readSetting': 'Настройки чтения',
      'preferenceSetting': 'Настройка предпочтений',
      'downloadSetting': 'Настройки загрузки',
      'networkSetting': 'Настройки сети',
      'performanceSetting': 'Настройки производительности',
      'mouseWheelSetting': 'Настройка колеса мыши',

      /// eh setting page
      'site': 'Сайт',
      'redirect2Eh': 'Перенаправлять на EH, если доступно',
      'redirect2EhHint': 'Сначала пытаться загрузить страницу с деталями галереи с сайта EH для лучшей производительности сети',
      'redirectAllGallery': 'Перенаправлять все галереи на EH',
      'imDonorHint': 'Если вы донор, вы можете включить это, чтобы получить доступ к галереям на сайте EX',
      'profileSetting': 'Настройка профиля',
      'chooseProfileHint': 'Выберите профиль для использования в JHenTai',
      'siteSetting': 'Настройки сайта',
      'siteSettingHint': 'Редактировать настройки сайта на e-hentai',
      'showCookie': 'Показать Cookie',
      'redirect2EH': 'Перенаправлять на сайт EH, если доступно',
      'redirect2Hints': 'Будет пытаться сначала парсить сайт EH',
      'pleaseLogInToOperate': 'Пожалуйста, войдите для выполнения операции',
      'imageLimits': 'Квота изображений',
      'resetCost': 'Долгий тап для сброса, стоимость',
      'assets': 'Активы',
      'isNotDonator': 'Не-доноры не могут просматривать квоту',
      'fetchImageQuotaFailed': 'Не удалось получить квоту изображений',

      /// tag setting page
      'myTags': 'Мои теги',
      'myTagsHint': 'Управление отслеживаемыми и скрытыми тегами онлайн',
      'localTags': 'Локальные теги',
      'localTagsHint': 'Дополнительные фильтрующие теги',
      'localTagsHint2': 'Галереи с этими тегами будут скрыты',
      'addLocalTags': 'Добавить теги',
      'hidden': 'Скрытый',
      'nope': 'Нет',
      'getTagSetFailed': 'Не удалось получить набор тегов',
      'updateTagSetFailed': 'Не удалось обновить набор тегов',
      'updateTagFailed': 'Не удалось обновить тег',
      'deleteTagSuccess': 'Тег успешно удален',
      'deleteTagFailed': 'Не удалось удалить тег',
      'addLocalTagHint': 'Искать для добавления нового тега',

      /// Profile Setting page
      'selectedProfile': 'Выбранный профиль',
      'resetIfSwitchSite': 'Будет сброшен при смене сайта',

      /// add host mapping dialog
      'addHostMapping': 'Добавить сопоставление хостов',

      /// Layout
      'layoutMode': 'Режим макета',
      'mobileLayoutV2Name': 'Мобильный',
      'mobileLayoutV2Desc': 'Одна колонка',
      'mobileLayoutName': 'Мобильный (старый)',
      'mobileLayoutDesc': 'Поддержка прекращена',
      'tabletLayoutV2Name': 'Планшетный',
      'tabletLayoutV2Desc': 'Две колонки',
      'tabletLayoutName': 'Планшетный (старый)',
      'tabletLayoutDesc': 'Поддержка прекращена',
      'desktopLayoutName': 'Десктопный',
      'desktopLayoutDesc': 'Две колонки с левой панелью вкладок, поддержка клавиатуры',

      /// style setting page
      'enableTagZHTranslation': 'Переводить названия тегов на китайский',
      'version': 'Версия',
      'downloadTagTranslationHint': 'Загрузка данных..., загружено: ',
      'zhTagSearchOrderOptimization': 'Правило сортировки автодополнения китайских тегов',
      'zhTagSearchOrderOptimizationHint': 'Интеллектуальная сортировка по умолчанию, по частоте, если включено',
      'themeMode': 'Тема оформления',
      'dark': 'Темная',
      'light': 'Светлая',
      'followSystem': 'Как в системе',
      'themeColor': 'Цвет темы',
      'listStyle': 'Стиль списка галерей (Глобально)',
      'flat': 'Плоский',
      'flatWithoutTags': 'Плоский (Без тегов)',
      'listWithoutTags': 'Карточки (Без тегов)',
      'listWithTags': 'Карточки',
      'waterfallFlowSmall': 'Плитка (Маленькая)',
      'waterfallFlowMedium': 'Плитка (Средняя)',
      'waterfallFlowBig': 'Плитка (Большая)',
      'crossAxisCountInWaterFallFlow': 'Количество колонок в плитке',
      'pageListStyle': 'Стиль списка галерей (Страница)',
      'crossAxisCountInGridDownloadPageForGroup': 'Кол-во колонок на стр. загрузок (Группа)',
      'crossAxisCountInGridDownloadPageForGallery': 'Кол-во колонок на стр. загрузок (Галерея)',
      'crossAxisCountInDetailPage': 'Кол-во колонок миниатюр на стр. деталей',
      'global': 'Глобально',
      'auto': 'Авто',
      'moveCover2RightSide': 'Переместить обложку вправо',
      'coverStyle': 'Стиль обложки',
      'cover': 'Обложка',
      'adaptive': 'Адаптивный',
      'simpleDashboardMode': 'Упрощенная главная страница',
      'simpleDashboardModeHint': 'Скрыть Рейтинги и Популярное',
      'hideBottomBar': 'Скрыть нижнюю панель',
      'hideScroll2TopButton': 'Скрыть кнопку "Наверх"',
      'whenScrollUp': 'При прокрутке вверх',
      'whenScrollDown': 'При прокрутке вниз',
      'preloadGalleryCover': 'Предзагружать обложки галерей',
      'preloadGalleryCoverHint': 'Предзагружать обложки галерей, еще не отображенных на странице',
      'enableSwipeBackGesture': 'Включить жест "Назад" свайпом',
      'enableLeftMenuDrawerGesture': 'Включить жест открытия левого меню',
      'enableQuickSearchDrawerGesture': 'Включить жест открытия быстрого поиска',
      'drawerGestureEdgeWidth': 'Ширина края для жеста меню',
      'alwaysShowScroll2TopButton': 'Всегда показывать кнопку "Наверх"',
      'enableDefaultFavorite': 'Включить избранное по умолчанию',
      'enableDefaultFavoriteHint': 'Долгий тап для перевыбора',
      'enableDefaultTagSet': 'Включить набор тегов по умолчанию',
      'enableDefaultTagSetHint': 'Долгий тап для перевыбора',
      'disableDefaultTagSetHint': 'Выбирать вручную',
      'launchInFullScreen': 'Запускать в полноэкранном режиме',
      'launchInFullScreenHint': 'Переключение вручную по F11',
      'disableDefaultFavoriteHint': 'Выбирать вручную',
      'searchBehaviour': 'Поведение поиска',
      'inheritAll': 'Наследовать все',
      'inheritAllHint': 'Использовать последние параметры для следующего поиска',
      'inheritPartially': 'Наследовать частично',
      'inheritPartiallyHint': 'Использовать последние параметры (кроме языка и категории)',
      'none': 'Нет',
      'noneHint': 'Использовать параметры по умолчанию для следующего поиска',
      'showAllGalleryTitles': 'Показывать все названия галерей',
      'showAllGalleryTitlesHint': 'Показывать оригинальное и японское названия, если доступны',
      'showGalleryTagVoteStatus': 'Показывать статус голосования за теги галереи',
      'showGalleryTagVoteStatusHint': 'Включая уверенность, скептицизм и неверность',
      'showComments': 'Показывать комментарии',
      'showAllComments': 'Показывать все комментарии',
      'showAllCommentsHint': 'По умолчанию показываются только 45 лучших и 5 последних',
      'addTag': 'Добавить тег',
      'addTagHint': 'Введите новые теги через запятую',

      /// theme color setting page
      'themeColorSettingHint': 'Задать разные цвета для светлой и темной тем',
      'preview': 'Предпросмотр',
      'preset': 'Предустановка',
      'custom': 'Пользовательский',

      /// performance setting page
      'maxGalleryNum4Animation': 'Макс. кол-во галерей для анимации списка на стр. загрузок',
      'maxGalleryNum4AnimationHint': 'Отключить анимацию для групп с большим кол-вом галерей (для стиля списка)',

      /// mouse wheel setting page
      'wheelScrollSpeed': 'Скорость прокрутки колесом',
      'ineffectiveInGalleryPage': 'Сейчас не действует на странице галереи.',

      /// advanced setting page
      'enableDomainFronting': 'Включить Domain Fronting',
      'bypassSNIBlocking': 'Обход блокировки SNI',
      'hostMapping': 'Сопоставление хостов',
      'hostMappingHint': 'Используется для domain fronting',
      'proxyAddress': 'Адрес прокси',
      'proxyAddressHint': 'Если вы используете прокси-сервер, настройте его правильно',
      'saveSuccess': 'Успешно сохранено',
      'saveFailed': 'Не удалось сохранить',
      'updateSuccess': 'Успешно обновлено',
      'connectTimeout': 'Тайм-аут подключения',
      'receiveTimeout': 'Тайм-аут получения данных',
      'pageCacheMaxAge': 'Время жизни кэша страниц',
      'pageCacheMaxAgeHint': 'Вы можете обновить кэш, обновив страницу',
      'cacheImageExpireDuration': 'Время жизни кэша изображений',
      'cacheImageExpireDurationHint': 'Автоматически удалять кэш изображений после запуска приложения',
      'oneMinute': '1 минута',
      'tenMinute': '10 минут',
      'oneHour': '1 час',
      'oneDay': '1 день',
      'threeDay': '3 дня',
      'enableLogging': 'Включить логирование',
      'enableVerboseLogging': 'Включить подробное логирование',
      'openLog': 'Открыть лог',
      'clearLogs': 'Очистить логи',
      'clearImagesCache': 'Очистить кэш изображений',
      'longPress2Clear': 'Долгий тап для очистки',
      'checkUpdateAfterLaunchingApp': 'Проверять обновления при запуске',
      'checkClipboard': 'Проверять URL галереи в буфере обмена',
      'clearPageCache': 'Очистить кэш страниц',
      'clearSuccess': 'Успешно очищено',
      'superResolution': 'Супер-разрешение изображений',
      'stopSuperResolution': 'Остановить супер-разрешение',
      'deleteSuperResolvedImage': 'Удалить обработанное изображение',
      'superResolveOriginalImageHint': 'Обработка оригинального изображения требует больше времени, места и ресурсов. Продолжить?',
      'verityAppLinks4Android12': 'Проверка ссылок приложений (Android 12+)',
      'verityAppLinks4Android12Hint': 'Для Android 12+ нужно вручную добавить ссылки в проверенные, чтобы открывать JHenTai из сторонних приложений',
      // Оставляем JHenTai
      'noImageMode': 'Режим без изображений',
      'exportData': 'Экспорт данных',
      'exportDataHint': 'Экспорт настроек, правил блокировки и истории',
      'selectExportItems': 'Выбрать элементы для экспорта',
      'importData': 'Импорт данных',
      'importDataHint': 'Приложение автоматически закроется после импорта для применения конфигурации',

      /// host mapping page
      'hostDataSource': 'По умолчанию менять не нужно.\nИсточник данных: https://dns.google/',
      // URL не переводим

      /// proxy page
      'proxySetting': 'Настройка прокси',
      'proxyType': 'Тип прокси',
      'systemProxy': 'Системный',
      'httpProxy': 'HTTP',
      'socks5Proxy': 'SOCKS5',
      'socks4Proxy': 'SOCKS4',
      'directProxy': 'Прямое',
      'address': 'Адрес',

      /// security setting page
      'enablePasswordAuth': 'Включить аутентификацию по паролю',
      'enableBiometricAuth': 'Включить биометрическую аутентификацию',
      'enableAuthOnResume': 'Включить аутентификацию при возобновлении',
      'enableAuthOnResumeHints': 'Задержка 3 секунды',
      'enableBlurBackgroundApp': 'Размывать фон при сворачивании',
      'hideImagesInAlbum': 'Скрывать изображения в альбоме',
      'hideImagesInAlbumHints': 'Если изменен путь загрузки по умолчанию, нужно создать .nomedia вручную',

      /// read setting page
      'enableImmersiveMode': 'Включить иммерсивный режим',
      'keepScreenAwakeWhenReading': 'Не выключать экран при чтении',
      'enableCustomReadBrightness': 'Включить пользовательскую яркость при чтении',
      'spaceBetweenImages': 'Пространство между изображениями',
      'enableImmersiveHint': 'Скрыть системную панель',
      'enableImmersiveHint4Windows': 'Скрыть заголовок окна',
      'deviceOrientation': 'Ориентация устройства',
      'landscape': 'Альбомная',
      'portrait': 'Портретная',
      'readDirection': 'Направление чтения',
      'notchOptimization': 'Оптимизация под вырез',
      'notchOptimizationHint': 'Добавить отступ перед первым изображением, чтобы избежать выреза и строки состояния',
      'imageRegionWidthRatio': 'Соотношение ширины области изображения',
      'gestureRegionWidthRatio': 'Соотношение ширины области жестов',
      'useThirdPartyViewer': 'Использовать сторонний просмотрщик',
      'thirdPartyViewerPath': 'Путь к стороннему просмотрщику (исп. файл)',
      'showThumbnails': 'Показывать миниатюры',
      'showScrollBar': 'Показывать полосу прокрутки',
      'showStatusInfo': 'Показывать статус внизу',
      'autoModeInterval': 'Интервал авто-режима',
      'autoModeStyle': 'Стиль авто-режима',
      'scroll': 'Прокрутка',
      'turnPage': 'Перелистывание',
      'top2bottomList': 'Сверху вниз (Непрерывно)',
      'left2rightSinglePage': 'Слева направо (Постранично)',
      'left2rightSinglePageFitWidth': 'Слева направо (По ширине)',
      'right2leftSinglePage': 'Справа налево (Постранично)',
      'right2leftSinglePageFitWidth': 'Справа налево (По ширине)',
      'left2rightDoubleColumn': 'Слева направо (Две колонки)',
      'right2leftDoubleColumn': 'Справа налево (Две колонки)',
      'left2rightList': 'Слева направо (Непрерывно)',
      'right2leftList': 'Справа налево (Непрерывно)',
      'enablePageTurnByVolumeKeys': 'Использовать клавиши громкости для перелистывания',
      'enablePageTurnAnime': 'Включить анимацию перелистывания',
      'enableDoubleTapToScaleUp': 'Включить двойной тап для увеличения',
      'enableTapDragToScaleUp': 'Включить тап с перетаскиванием для увеличения',
      'enableBottomMenu': 'Включить нижнее меню',
      'reverseTurnPageDirection': 'Обратное направление перелистывания',
      'disableGestureWhenScrolling': 'Отключить жесты при прокрутке',
      'disablePageTurningOnTap': 'Отключить перелистывание по тапу',
      'turnPageMode': 'Режим перелистывания',
      'turnPageModeHint': 'К следующему экрану или изображению',
      'enableImageMaxKilobytes': 'Включить сжатие изображений',
      'imageMaxKilobytes': 'Макс. размер изображения (КБ)',
      'imageMaxKilobytesHint': 'Изображения больше этого размера будут сжаты',
      'image': 'Изображение',
      'screen': 'Экран',
      'preloadDistanceInOnlineMode': 'Дистанция предзагрузки (Онлайн)',
      'preloadDistanceInLocalMode': 'Дистанция предзагрузки (Локально)',
      'ScreenHeight': 'Экран',
      'preloadPageCount': 'Кол-во предзагружаемых страниц (Онлайн)',
      'preloadPageCountInLocalMode': 'Кол-во предзагружаемых страниц (Локально)',
      'continuousScroll': 'Непрерывная прокрутка',
      'continuousScrollHint': 'Склеивать несколько изображений',
      'doubleColumn': 'Две колонки',
      'displayFirstPageAlone': 'Отображать первую страницу отдельно',
      'displayFirstPageAloneGlobally': 'Отображать первую страницу отдельно (Глобально)',
      'toggleFullScreen': 'Переключить полноэкранный режим',
      'enableAutoScaleUp': 'Включить авто-масштабирование длинных изображений',
      'enableAutoScaleUpHints': 'Сделать ширину изображения равной ширине экрана',

      /// preference setting page
      'showR18GImageDirectly': 'Показывать R18G изображения сразу',
      'defaultTab': 'Вкладка по умолчанию',
      'showUtcTime': 'Показывать UTC время для галерей',
      'showDawnInfo': 'Показывать событие "Новый рассвет"',
      'showEncounterMonster': 'Показывать событие "Встреча с монстром" (HentaiVerse)',

      /// log page
      'logList': 'Список логов',

      /// super resolution setting page
      'downloadSuperResolutionModelHint': 'Скачать модель с Github',
      'modelDirectoryPath': 'Путь к каталогу моделей',
      'upSamplingScale': 'Масштаб увеличения',
      'modelType': 'Выбрать модель',
      'x4plusHint': 'Занимает больше места, но обычно быстрее',
      'x4plusAnimeHint': 'Занимает меньше места, но обычно медленнее',
      'enable4OnlineReading': 'Обрабатывать автоматически при чтении онлайн',

      /// download page
      'local': 'Локально',
      'reDownload': 'Перекачать',
      'delete': 'Удалить',
      'deleteTask': 'Удалить только задачу',
      'deleteTaskAndImages': 'Удалить задачу и изображения',
      'unlocking': 'Разблокировка',
      'unlocked': 'Разблокировано',
      'parsingDownloadPageUrl': 'Парсинг Ⅰ',
      'parsedDownloadPageUrl': 'Парсинг Ⅰ',
      'parsingDownloadUrl': 'Парсинг Ⅱ',
      'parsedDownloadUrl': 'Парсинг Ⅱ',
      'waitingIsolate': 'Ожидание',
      'downloaded': 'Загружено',
      'downloadFailed': 'Ошибка загрузки',
      'unpacking': 'Распаковка',
      'completed': 'Завершено',
      'needReUnlock': 'Нужна повторная разблокировка',
      'reUnlock': 'Разблокировать заново',
      'reUnlockHint': 'Внимание! Повторная разблокировка требует повторной покупки архива.',
      'downloadHelpInfo': 'Если вы не можете скачать и видите ошибки типа "table doesn\'t exist" в логах, удалите и переустановите приложение.',
      'localGalleryHelpInfo': 'Загрузка галерей, скачанных не через JHenTai. Добавьте путь в Настройки загрузки -> Доп. путь сканирования, затем обновите.',
      'localGalleryHelpInfo4iOSAndMacOS': 'Загрузка галерей, скачанных не через JHenTai. Поместите галереи в путь загрузки по умолчанию, затем обновите.',
      'deleteLocalGalleryHint': 'Удалить ваши локальные файлы',
      'priority': 'Приоритет',
      'highest': 'Высший',
      'default': 'По умолчанию',
      'newGalleryCount': 'Кол-во новых галерей',
      'changePriority': 'Изменить приоритет',
      'changeGroup': 'Изменить группу',
      'chooseGroup': 'Выбрать группу',
      'renameGroup': 'Переименовать группу',
      'deleteGroup': 'Удалить группу',
      'existingGroup': 'Существующая группа',
      'groupName': 'Название группы',
      'drag2sort': 'Перетащить для сортировки',
      'switch2GridMode': 'Переключить в режим сетки',
      'switch2ListMode': 'Переключить в режим списка',
      'multiSelect': 'Множественный выбор',
      'multiSelectHint': 'Нажмите для выбора',
      'resumeAllTasks': 'Возобновить все задачи',
      'pauseAllTasks': 'Приостановить все задачи',
      'requireDownloadComplete': 'Требуется завершение загрузки',
      'operationHasCompleted': 'Операция завершена',
      'operationInProgress': 'Операция в процессе',
      'startProcess': 'Начать процесс',
      'multiReDownloadHint': 'Вы перекачаете все выбранные галереи.',
      'multiChangeGroupHint': 'Вы измените группу для всех выбранных галерей.',
      'multiDeleteHint': 'Вы удалите все выбранные галереи.',
      'peakHoursHint': 'Загрузка оригинальных файлов в часы пик требует GP, у вас недостаточно. Загрузка приостановлена.',
      // GP - термин EH
      'oldGalleryHint': 'Загрузка оригинальных файлов этой галереи требует GP, у вас недостаточно.',
      // GP - термин EH
      'exceedLimitHint': 'Вы достигли лимита изображений и не имеете достаточно GP для покупки квоты.',
      // GP - термин EH
      'deleteUpdatingDependentHint': 'Обновление другой галереи зависит от текущей, лучше удалить после завершения обновления.',
      'migrateToDownload': 'Перенести в 「Загрузки」',
      'refresh': 'Обновить',

      /// download search page
      'simpleSearch': 'Простой',
      'regexSearch': 'Regex',

      /// search dialog
      'searchConfig': 'Настройка поиска',
      'addTabBar': 'Добавить вкладку',
      'updateTabBar': 'Обновить вкладку',
      'addQuickSearch': 'Добавить',
      'updateQuickSearch': 'Обновить',
      'filter': 'Фильтр',
      'tabBarName': 'Имя вкладки',
      'quickSearchName': 'Имя',
      'pleaseInputValidName': 'Пожалуйста, введите допустимое имя',
      'sameNameExists': 'Имя уже существует',
      'sameConfigExists': 'Конфигурация уже существует',
      'searchType': 'Тип поиска',
      'popular': 'Популярное',
      'ranklist': 'Рейтинг',
      'ranklistBoard': 'Доска рейтинга',
      'watched': 'Отслеживаемое',
      'history': 'История',
      'keyword': 'Ключевое слово',
      'orderBy': 'Сортировать по',
      'favoritedTime': 'Времени добавления в избранное',
      'publishedTime': 'Времени публикации',
      'backspace2DeleteTag': 'Backspace для удаления тега',
      'searchGalleryName': 'Искать название галереи',
      'searchGalleryTags': 'Искать теги галереи',
      'searchGalleryDescription': 'Искать описание галереи',
      'onlySearchExpungedGalleries': "Искать только удаленные галереи",
      'onlyShowGalleriesWithTorrents': 'Показывать только галереи с торрентами',
      'searchLowPowerTags': 'Искать теги Low Power',
      'searchDownVotedTags': 'Искать теги с отрицательным рейтингом',
      'pageAtLeast': 'Страниц минимум',
      'pageAtMost': 'Страниц максимум',
      'pagesBetween': 'Страниц между',
      'pageRangeSelectHint': 'мин <= 1000, макс >= 10\nмин/макс <= 0.8, макс-мин >= 20',
      'to': 'до',
      'minimumRating': 'Минимальный рейтинг',
      'disableFilterForLanguage': 'Отключить фильтр по языку',
      'disableFilterForUploader': 'Отключить фильтр по загрузившему',
      'disableFilterForTags': 'Отключить фильтр по тегам',
      'searchName': 'Искать имя',
      'searchTags': 'Искать теги',
      'searchNote': 'Искать заметку',
      'allTime': 'Все время',
      'year': 'Год',
      'month': 'Мес',
      'day': 'День',
      'favoriteHint': 'Квалификаторы: tag/title/comment/favnote',

      /// popular page
      'getPopularListFailed': 'Не удалось получить список популярного',

      /// ranklist page
      'getRanklistFailed': 'Не удалось получить список рейтинга',
      'getSomeOfGallerysFailed': 'Не удалось получить некоторые галереи',

      /// history page
      'getHistoryGallerysFailed': 'Не удалось получить галереи из истории',

      /// search page
      'search': 'Поиск',
      'searchFailed': 'Ошибка поиска',
      'fileSearchFailed': 'Ошибка поиска по файлу',
      'tab': 'Вкладка',
      'openGallery': 'Открыть галерею',
      'tapChip2Delete': 'Нажмите на чип для удаления\nДолгий тап на кнопку для удаления всего',
      'accurateCountTemplate': '%s результатов',
      'hundredsOfCountTemplate': 'Сотни результатов',
      'thousandsOfCountTemplate': 'Тысячи результатов',

      /// about page
      'author': 'Автор',
      'Q&A': 'Вопросы и ответы',
      'telegramHint': 'Вы можете сначала задать вопросы на Github',

      /// download setting page
      'downloadPath': 'Путь загрузки',
      'changeDownloadPathHint':
          'Долгий тап для изменения (не используйте SD-карту или системные пути). Скачанные галереи будут скопированы автоматически, старые файлы сохранены. При ошибках попробуйте сбросить.',
      'resetDownloadPath': 'Сбросить путь загрузки',
      'extraGalleryScanPath': 'Доп. путь сканирования галерей',
      'extraGalleryScanPathHint': 'Для сканирования и загрузки локальных галерей',
      'singleImageSavePath': 'Путь сохранения отдельных изображений',
      'downloadOriginalImage': 'Оригинальное изображение',
      'downloadOriginalImageByDefault': 'Выбирать оригинальное изображение по умолчанию',
      'originalImage': 'Оригинал',
      'resampleImage': 'Уменьшенное',
      'defaultGalleryGroup': 'Группа галерей по умолчанию',
      'defaultArchiveGroup': 'Группа архивов по умолчанию',
      'never': 'Никогда',
      'manual': 'Вручную',
      'always': 'Всегда',
      'longPress2Reset': 'Долгий тап для сброса',
      'needPermissionToChangeDownloadPath': 'Нужно разрешение для изменения пути загрузки',
      'invalidPath': 'Недопустимый путь. Избегайте SD-карт, системных путей или корневого каталога.',
      'downloadTaskConcurrency': 'Параллельные загрузки',
      'needRestart': 'Требуется перезапуск',
      'speedLimit': 'Ограничение скорости',
      'speedLimitHint': 'Не качайте слишком быстро',
      'per': 'за',
      'images': 'изображений',
      'downloadTimeout': 'Тайм-аут загрузки',
      'downloadAllGallerysOfSamePriority': 'Загружать все галереи одного приоритета',
      'downloadAllGallerysOfSamePriorityHint': 'По умолчанию загружать только 1 галерею одновременно в 1 группе с высшим приоритетом',
      'alwaysUseDefaultGroup': 'Всегда использовать группу по умолчанию',
      'enableStoreMetadataForRestore': 'Включить сохранение метаданных для восстановления',
      'enableStoreMetadataForRestoreHint': 'Если отключено, вы не сможете восстановить задачи загрузки',
      'archiveDownloadIsolateCount': 'Кол-во потоков загрузки архивов',
      'archiveDownloadIsolateCountHint': 'Сумма потоков для всех задач должна быть < 10, иначе загрузка не удастся',
      'manageArchiveDownloadConcurrency': 'Управлять параллелизмом загрузки архивов',
      'manageArchiveDownloadConcurrencyHint': 'Архив будет ждать, пока не освободятся потоки для загрузки',
      'deleteArchiveFileAfterDownload': 'Удалять ZIP-файл архива после загрузки',
      'restoreDownloadTasks': 'Восстановить задачи загрузки',
      'restoreDownloadTasksHint': 'Восстановить задачи загрузки по метаданным',
      'restoreDownloadTasksSuccess': 'Задачи загрузки успешно восстановлены',
      'restoredCount': 'Восстановлено задач',
      'restoredGalleryCount': 'Восстановлено галерей',
      'restoredArchiveCount': 'Восстановлено архивов',
      'restoreTasksAutomatically': 'Восстанавливать задачи автоматически',
      'restoreTasksAutomaticallyHint': 'Восстанавливать задачи автоматически при запуске приложения',
      'brokenDownloadPathHint': 'Похоже, ваш путь загрузки поврежден, функция загрузки может не работать',
      'brokenExtraScanPathHint': 'Похоже, ваш путь к локальным галереям поврежден, локальные галереи могут не распознаваться',
      'useJH2UpdateGallery': 'Use JH server to accelerate gallery updates',

      /// archive bot settings
      'archiveBotSettings': 'Archive Bot Settings',
      'archiveBotSettingsHint': 'Use archive bot to get archive links for free',
      'apiSetting': 'API Setting',
      'apiAddress': 'Address',
      'apiKey': 'Key',
      'apiKeyHint': 'Enter the key you got from Telegram bot',
      'dailyCheckin': 'Daily Check-in',
      'currentBalance': 'Current GP Balance',
      'checkBalanceFailed': 'Failed to get GP balance',
      'checkInFailed': 'Check-in failed',
      'checkInSuccess': 'Check-in success',
      'checkInSuccessHint': 'Got GP: %s, current total GP: %s.',
      'pauseDownloadByInvalidArchiveBotKey': 'Archive bot settings is invalid, download paused',
      'chooseArchiveParseSource': 'Change Parse Source',
      'official': 'Official',
      'archiveBot': 'Archive Bot',
      'changeParseSource2Official': 'Change parse source to official',
      'changeParseSource2Bot': 'Change parse source to archive bot',
      'invalidParam': 'Invalid parameter',
      'invalidApiKey': 'Invalid API key',
      'banned': 'You have been banned',
      'fetchGalleryInfoFailed': 'Failed to get gallery info',
      'insufficientGP': 'Insufficient GP',
      'parseFailed': 'Parse failed',
      'checkedIn': 'Already checked in today',
      'serverError': 'Archive bot internal error',
      'useProxyServer': 'Use JHenTai Proxy Server',
      'useProxyServerHint': 'Route requests through JHenTai server',

      /// password setting dialog
      'setPasswordHint': 'Пожалуйста, введите ваш пароль',
      'confirmPasswordHint': 'Пожалуйста, введите ваш пароль еще раз',
      'passwordNotMatchHint': 'Пароли не совпадают, попробуйте снова',

      /// cloud setting page
      'serverCondition': 'Состояние сервера',
      'configSync': 'Синхронизация настроек',
      'configSyncHint': 'Хранить ваши настройки в облаке (до 50 записей)',
      'upload2cloud': 'Загрузить в облако',
      'upload2cloudHint': 'Загрузить вашу текущую локальную конфигурацию',
      'tap2upload': 'Нажмите для загрузки',
      'copyIdentificationCodeSuccess': 'Загружено успешно. Идентификационный код скопирован',
      'copyShareCode': 'Скопировать код для обмена',
      'import': 'Импорт',
      'save2Local': 'Сохранить локально',
      'readIndexRecord': 'Прогресс чтения',
      'quickSearch': 'Быстрый поиск',
      'blockRules': 'Правила блокировки',
      'searchHistory': 'История поиска',
      'galleryHistory': 'История галерей',

      /// block rule page / Страница правил блокировки
      'configureBlockRuleFailed': 'Не удалось настроить правило блокировки',
      'removeBlockRuleFailed': 'Не удалось удалить правило блокировки',
      'inputNumberHint': 'Пожалуйста, введите корректное число',
      'inputRegexHint': 'Пожалуйста, введите корректное регулярное выражение',
      'useBuiltInBlockedUsers': 'Включить встроенный список заблокированных пользователей',
      'useBuiltInBlockedUsersHint': 'Фильтровать комментарии от пользователей из списка блокировки',
      'blockingRules': 'Правила блокировки',
      'blockingRulesHint': 'Дополнительные правила блокировки для галерей и комментариев',
      'blockingTarget': 'Цель блокировки',
      'blockingAttribute': 'Атрибут блокировки',
      'blockingPattern': 'Шаблон блокировки',
      'blockingExpression': 'Выражение блокировки',
      'contain': 'Содержит',
      'notContain': 'Не содержит',
      'regex': 'Regex',
      'comment': 'Комментарий',
      'tag': 'Тег',
      'userId': 'UserId',
      'content': 'Содержимое',
      'incompleteInformation': 'Неполная информация',
      'noBlockingRuleHint': 'Добавьте хотя бы 1 правило',
      'notSameBlockingRuleTargetHint': 'Все подправила должны иметь одну и ту же цель блокировки',
      'blockingRuleHelp': '''
Цель блокировки: Фильтровать галереи в списке или комментарии на странице деталей. Все подправила в одном правиле должны иметь одну цель.
Атрибут блокировки: Указывает атрибут цели, по которому пишется правило.
Шаблон блокировки: Используйте регулярные выражения для сложных случаев.
Выражение блокировки: Простые строки или регулярные выражения.

Заметка 1: Разные правила имеют отношение ИЛИ (||), подправила в одном правиле - И (&&).
Заметка 2: При блокировке тега правило проверяет каждый тег галереи, выражение должно быть для одного тега.
Заметка 3: При блокировке тега нужно указывать полный тег с пространством имен, если используется правило '='.
Заметка 4: Нужно использовать макет галереи, отображающий все теги (например, "Расширенный"), иначе некоторые галереи могут не фильтроваться.

Пример 1: Блокировать галереи с тегом "yaoi" и без тега "tomgirl" -> Галерея Тег Содержит male:yaoi && Галерея Тег Не содержит male:tomgirl
Пример 2: Блокировать комментарии с оценкой не выше 10 -> Комментарий Оценка <= 10
    ''',

      /// quick search page
      'quickSearch': 'Быстрый поиск',

      /// dashboard page
      'seeAll': 'Все',
      'newest': 'Новейшие',

      /// torrent dialog
      'outdated': 'Устарел',

      /// tag dialog
      'warningImageHint': 'R18G изображение, Нажмите для просмотра',

      /// tagSet dialog
      'chooseTagSet': 'Выбрать набор тегов',

      /// tag namespace
      'language': 'Язык',
      'artist': 'Художник',
      'character': 'Персонаж',
      'female': 'Женский',
      'male': 'Мужской',
      'parody': 'Пародия',
      'group': 'Группа',
      'mixed': 'Смешанное',
      'Coser': 'Косплеер?',
      'cosplayer': 'Косплеер',
      'reclass': 'Переклассификация',
      'temp': 'Временный',
      'other': 'Другое',
    };
  }
}
