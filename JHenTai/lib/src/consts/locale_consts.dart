class LocaleConsts {
  static const Map<String, String> language2Abbreviation = {
    'chinese': 'ZH',
    'japanese': 'JP',
    'english': 'EN',
    'korean': 'KR',
    'spanish': 'ES',
    'portuguese': 'PT',
    'russian': 'RU',
    'french': 'FR',
    'italian': 'IT',
    'german': 'DE',
    'polish': 'PL',
    'hungarian': 'HU',
    'thai': 'TH',
    'dutch': 'NL',
    'vietnamese': 'VI',
  };

  static Map<String, String> localeCode2Description = {
    'zh_CN': '简体中文',
    'zh_TW': '繁體中文(台灣)',
    'en_US': 'English',
    'pt_BR': 'Português brasileiro',
    'ko_KR': '한국어',
    'ru_RU': 'Русский',
  };
}
